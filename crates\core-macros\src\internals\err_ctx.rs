use quote::ToTokens;
use std::cell::RefCell;
use std::fmt::Display;
use std::thread;

/// A context structure designed for efficient error management during code processing.
///
/// The `ErrorContext` struct provides methods to collect and manage errors that occur during
/// various operations. It can accumulate errors with associated spans, add existing syntax errors,
/// and perform error checking at the end of a code processing stage.
///
/// # Examples
///
/// ```no_run
/// # use crate::internals::ErrorContext;
/// let error_ctx = ErrorContext::new();
///
/// // Perform operations that may generate errors
/// // ...
///
/// // Add errors to the context
/// // error_ctx.add_error_with_span(some_object, "An error occurred.");
/// // error_ctx.add_syntax_error(syntax_error);
///
/// // Perform error checking
/// let result = error_ctx.check_errors();
/// if result.is_err() {
///     // Handle errors gracefully
///     // ...
/// }
/// ```
pub struct ErrorContext {
    /// A container for collecting errors during processing.
    errors: RefCell<Option<Vec<syn::Error>>>,
}

impl Default for ErrorContext {
    fn default() -> Self {
        Self::new()
    }
}

impl ErrorContext {
    /// Create a new instance of `ErrorContext`.
    ///
    /// Returns a new, empty `ErrorContext` with error collection initialized.
    pub fn new() -> Self {
        ErrorContext {
            errors: RefCell::new(Some(Vec::new())),
        }
    }

    /// Add a new error associated with a specific span.
    ///
    /// This method adds a new error to the context, associating it with the provided object's
    /// span. The error message is also specified.
    pub fn add_error_with_span<A: ToTokens, T: Display>(&self, obj: A, msg: T) {
        self.errors
            .borrow_mut()
            .as_mut()
            .unwrap()
            .push(syn::Error::new_spanned(obj.into_token_stream(), msg));
    }

    /// Add an existing syntax error to the context.
    ///
    /// This method allows adding a pre-existing `syn::Error` instance to the error collection.
    #[allow(unused)]
    pub fn add_syntax_error(&self, err: syn::Error) {
        self.errors.borrow_mut().as_mut().unwrap().push(err);
    }

    /// Check for collected errors and return a combined result.
    ///
    /// This method performs error checking on the accumulated errors and returns a `syn::Result`
    /// indicating success or the combined error if any errors were collected.
    pub fn check_errors(self) -> syn::Result<()> {
        let mut errors = self.errors.borrow_mut().take().unwrap().into_iter();
        let mut combined_error = match errors.next() {
            Some(first) => first,
            None => return Ok(()), // No errors, return Ok
        };

        // Combine remaining errors into the combined error
        for rest in errors {
            combined_error.combine(rest);
        }

        // Return the combined error as an Err result
        Err(combined_error)
    }
}

impl Drop for ErrorContext {
    fn drop(&mut self) {
        if !thread::panicking() && self.errors.borrow().is_some() {
            panic!("forgot to check for errors");
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    use syn::{Ident, parse_quote};

    /// Helper function to create a test identifier
    fn test_ident() -> Ident {
        parse_quote!(test_identifier)
    }

    /// Helper function to create a test error
    fn test_error() -> syn::Error {
        syn::Error::new_spanned(test_ident(), "test error message")
    }

    #[test]
    fn test_error_context_new() {
        let ctx = ErrorContext::new();

        // Verify that the context is properly initialized
        assert!(ctx.errors.borrow().is_some());
        assert!(ctx.errors.borrow().as_ref().unwrap().is_empty());

        // Must call check_errors to avoid Drop panic
        let result = ctx.check_errors();
        assert!(result.is_ok());
    }

    #[test]
    fn test_error_context_default() {
        let ctx = ErrorContext::default();

        // Verify that default initialization works
        assert!(ctx.errors.borrow().is_some());
        assert!(ctx.errors.borrow().as_ref().unwrap().is_empty());

        // Must call check_errors to avoid Drop panic
        let result = ctx.check_errors();
        assert!(result.is_ok());
    }

    #[test]
    fn test_add_error_with_span() {
        let ctx = ErrorContext::new();
        let ident = test_ident();
        let message = "test error message";

        ctx.add_error_with_span(&ident, message);

        // Verify that the error was added
        {
            let errors = ctx.errors.borrow();
            let error_vec = errors.as_ref().unwrap();
            assert_eq!(error_vec.len(), 1);

            let error = &error_vec[0];
            assert_eq!(error.to_string(), message);
        }

        // Must call check_errors to avoid Drop panic
        let result = ctx.check_errors();
        assert!(result.is_err());
    }

    #[test]
    fn test_add_error_with_span_multiple_types() {
        let ctx = ErrorContext::new();

        // Test with different types that implement ToTokens
        let ident: Ident = parse_quote!(test_ident);
        let literal: syn::LitStr = parse_quote!("test_string");
        let expr: syn::Expr = parse_quote!(1 + 2);

        ctx.add_error_with_span(&ident, "identifier error");
        ctx.add_error_with_span(&literal, "literal error");
        ctx.add_error_with_span(&expr, "expression error");

        {
            let errors = ctx.errors.borrow();
            let error_vec = errors.as_ref().unwrap();
            assert_eq!(error_vec.len(), 3);

            assert_eq!(error_vec[0].to_string(), "identifier error");
            assert_eq!(error_vec[1].to_string(), "literal error");
            assert_eq!(error_vec[2].to_string(), "expression error");
        }

        // Must call check_errors to avoid Drop panic
        let result = ctx.check_errors();
        assert!(result.is_err());
    }

    #[test]
    fn test_add_error_with_span_different_message_types() {
        let ctx = ErrorContext::new();
        let ident = test_ident();

        // Test with different message types that implement Display
        ctx.add_error_with_span(&ident, "string literal");
        ctx.add_error_with_span(&ident, String::from("owned string"));
        ctx.add_error_with_span(&ident, 42);
        ctx.add_error_with_span(&ident, format!("formatted {}", "string"));

        {
            let errors = ctx.errors.borrow();
            let error_vec = errors.as_ref().unwrap();
            assert_eq!(error_vec.len(), 4);

            assert_eq!(error_vec[0].to_string(), "string literal");
            assert_eq!(error_vec[1].to_string(), "owned string");
            assert_eq!(error_vec[2].to_string(), "42");
            assert_eq!(error_vec[3].to_string(), "formatted string");
        }

        // Must call check_errors to avoid Drop panic
        let result = ctx.check_errors();
        assert!(result.is_err());
    }

    #[test]
    fn test_add_syntax_error() {
        let ctx = ErrorContext::new();
        let error = test_error();
        let error_message = error.to_string();

        ctx.add_syntax_error(error);

        // Verify that the error was added
        {
            let errors = ctx.errors.borrow();
            let error_vec = errors.as_ref().unwrap();
            assert_eq!(error_vec.len(), 1);
            assert_eq!(error_vec[0].to_string(), error_message);
        }

        // Must call check_errors to avoid Drop panic
        let result = ctx.check_errors();
        assert!(result.is_err());
    }

    #[test]
    fn test_add_multiple_syntax_errors() {
        let ctx = ErrorContext::new();

        let error1 = syn::Error::new_spanned(test_ident(), "first error");
        let error2 = syn::Error::new_spanned(test_ident(), "second error");
        let error3 = syn::Error::new_spanned(test_ident(), "third error");

        ctx.add_syntax_error(error1);
        ctx.add_syntax_error(error2);
        ctx.add_syntax_error(error3);

        {
            let errors = ctx.errors.borrow();
            let error_vec = errors.as_ref().unwrap();
            assert_eq!(error_vec.len(), 3);

            assert_eq!(error_vec[0].to_string(), "first error");
            assert_eq!(error_vec[1].to_string(), "second error");
            assert_eq!(error_vec[2].to_string(), "third error");
        }

        // Must call check_errors to avoid Drop panic
        let result = ctx.check_errors();
        assert!(result.is_err());
    }

    #[test]
    fn test_check_errors_no_errors() {
        let ctx = ErrorContext::new();

        let result = ctx.check_errors();
        assert!(result.is_ok());
    }

    #[test]
    fn test_check_errors_single_error() {
        let ctx = ErrorContext::new();
        let error_message = "single error";

        ctx.add_error_with_span(test_ident(), error_message);

        let result = ctx.check_errors();
        assert!(result.is_err());

        let error = result.unwrap_err();
        assert_eq!(error.to_string(), error_message);
    }

    #[test]
    fn test_check_errors_multiple_errors() {
        let ctx = ErrorContext::new();

        ctx.add_error_with_span(test_ident(), "first error");
        ctx.add_error_with_span(test_ident(), "second error");
        ctx.add_error_with_span(test_ident(), "third error");

        let result = ctx.check_errors();
        assert!(result.is_err());

        let combined_error = result.unwrap_err();
        let error_string = combined_error.to_string();

        // The combined error should contain the first error message
        // Note: syn::Error::combine behavior may vary, so we test what we can guarantee
        assert!(error_string.contains("first error"));
        // Additional errors may or may not be visible in the string representation
        // depending on syn's implementation
    }

    #[test]
    fn test_mixed_error_types() {
        let ctx = ErrorContext::new();

        // Add both span-based and syntax errors
        ctx.add_error_with_span(test_ident(), "span error");
        ctx.add_syntax_error(syn::Error::new_spanned(test_ident(), "syntax error"));

        let result = ctx.check_errors();
        assert!(result.is_err());

        let combined_error = result.unwrap_err();
        let error_string = combined_error.to_string();

        // At least the first error should be present
        assert!(error_string.contains("span error"));
        // The second error may or may not be visible depending on syn's combine implementation
    }

    #[test]
    fn test_error_context_consumes_self() {
        let ctx = ErrorContext::new();
        ctx.add_error_with_span(test_ident(), "test error");

        // check_errors should consume self
        let _result = ctx.check_errors();

        // ctx is no longer accessible after check_errors
        // This test verifies the method signature is correct
    }

    #[test]
    fn test_error_context_with_complex_tokens() {
        let ctx = ErrorContext::new();

        // Test with more complex token structures
        let struct_def: syn::ItemStruct = parse_quote! {
            struct TestStruct {
                field1: i32,
                field2: String,
            }
        };

        let function_def: syn::ItemFn = parse_quote! {
            fn test_function(param: i32) -> String {
                format!("{}", param)
            }
        };

        ctx.add_error_with_span(&struct_def, "struct error");
        ctx.add_error_with_span(&function_def, "function error");

        let result = ctx.check_errors();
        assert!(result.is_err());

        let error_string = result.unwrap_err().to_string();
        // At least the first error should be present
        assert!(error_string.contains("struct error"));
        // The second error may or may not be visible depending on syn's combine implementation
    }

    #[test]
    fn test_error_context_empty_message() {
        let ctx = ErrorContext::new();

        ctx.add_error_with_span(test_ident(), "");

        let result = ctx.check_errors();
        assert!(result.is_err());

        // Even empty messages should create valid errors
        let error = result.unwrap_err();
        assert_eq!(error.to_string(), "");
    }

    #[test]
    fn test_error_context_unicode_messages() {
        let ctx = ErrorContext::new();

        ctx.add_error_with_span(test_ident(), "Unicode: 🦀 Rust error");
        ctx.add_error_with_span(test_ident(), "中文错误信息");
        ctx.add_error_with_span(test_ident(), "Ошибка на русском");

        let result = ctx.check_errors();
        assert!(result.is_err());

        let error_string = result.unwrap_err().to_string();
        // At least the first error should be present
        assert!(error_string.contains("🦀 Rust error"));
        // Additional unicode errors may or may not be visible depending on syn's combine implementation
    }

    #[test]
    fn test_error_context_very_long_message() {
        let ctx = ErrorContext::new();

        let long_message = "a".repeat(1000);
        ctx.add_error_with_span(test_ident(), &long_message);

        let result = ctx.check_errors();
        assert!(result.is_err());

        let error = result.unwrap_err();
        assert_eq!(error.to_string(), long_message);
    }

    #[test]
    fn test_error_context_borrow_checker() {
        let ctx = ErrorContext::new();

        // Test that we can add errors while borrowing the context
        {
            let _borrow = ctx.errors.borrow();
            // This should not cause borrow checker issues
        }

        ctx.add_error_with_span(test_ident(), "test error");

        let result = ctx.check_errors();
        assert!(result.is_err());
    }

    /// Test that demonstrates the Drop implementation behavior
    /// Note: This test is commented out because it would panic in normal test runs
    /// Uncomment to manually test the Drop behavior
    /*
    #[test]
    #[should_panic(expected = "forgot to check for errors")]
    fn test_drop_panic_when_errors_not_checked() {
        let ctx = ErrorContext::new();
        ctx.add_error_with_span(test_ident(), "unchecked error");
        // ctx goes out of scope without calling check_errors()
        // This should panic in the Drop implementation
    }
    */

    #[test]
    fn test_drop_no_panic_when_errors_checked() {
        let ctx = ErrorContext::new();
        ctx.add_error_with_span(test_ident(), "checked error");

        // Check errors to prevent panic in Drop
        let _result = ctx.check_errors();

        // No panic should occur when ctx goes out of scope
    }

    #[test]
    fn test_drop_no_panic_when_no_errors() {
        let ctx = ErrorContext::new();

        // Check errors even when there are none
        let result = ctx.check_errors();
        assert!(result.is_ok());

        // No panic should occur when ctx goes out of scope
    }

    #[test]
    fn test_error_context_thread_safety() {
        // Test that ErrorContext works correctly in single-threaded context
        // (RefCell is not Send/Sync, so this is expected behavior)
        let ctx = ErrorContext::new();

        for i in 0..10 {
            ctx.add_error_with_span(test_ident(), format!("error {}", i));
        }

        let result = ctx.check_errors();
        assert!(result.is_err());

        let error_string = result.unwrap_err().to_string();
        // At least the first error should be present
        assert!(error_string.contains("error 0"));
        // Additional errors may or may not be visible depending on syn's combine implementation
    }

    #[test]
    fn test_error_context_performance_many_errors() {
        let ctx = ErrorContext::new();

        // Add many errors to test performance
        for i in 0..1000 {
            ctx.add_error_with_span(test_ident(), format!("error {}", i));
        }

        let result = ctx.check_errors();
        assert!(result.is_err());

        // Just verify that it completes without issues
        let error_string = result.unwrap_err().to_string();
        // At least the first error should be present
        assert!(error_string.contains("error 0"));
        // The last error may or may not be visible depending on syn's combine implementation
    }
}
