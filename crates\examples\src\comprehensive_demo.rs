use ratatui::{
    Terminal,
    backend::CrosstermBackend,
    crossterm::{
        event::{self, DisableMouseCapture, EnableMouseCapture, Event, KeyCode, KeyEventKind},
        execute,
        terminal::{EnterAlternateScreen, LeaveAlternateScreen, disable_raw_mode, enable_raw_mode},
    },
    layout::{Alignment, Constraint, Direction},
    style::{Color, Modifier, Style},
    widgets::Borders,
};
use rink::prelude::*;
use std::{
    collections::VecDeque,
    io,
    time::{Duration, Instant},
};

/// Professional System Monitoring Dashboard
///
/// This comprehensive example demonstrates the full capabilities of the rink-leptos
/// RSX framework for building complex terminal user interfaces. Features include:
///
/// - Real-time system monitoring with simulated data
/// - Multi-panel dashboard layout with responsive design
/// - Interactive navigation between different views
/// - Live data visualization with historical tracking
/// - Professional styling and user experience
/// - Comprehensive error handling and state management
///
/// Navigation:
/// - Tab/Shift+Tab: Navigate between panels
/// - 1-5: Switch between different dashboard views
/// - Space: Pause/Resume real-time updates
/// - R: Reset all metrics
/// - Q: Quit application
fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Setup terminal with proper error handling
    enable_raw_mode()?;
    let mut stdout = io::stdout();
    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    // Create and run the comprehensive dashboard
    let app = SystemDashboard::new();
    let res = run_dashboard(&mut terminal, app);

    // Restore terminal state
    disable_raw_mode()?;
    execute!(
        terminal.backend_mut(),
        LeaveAlternateScreen,
        DisableMouseCapture
    )?;
    terminal.show_cursor()?;

    if let Err(err) = res {
        eprintln!("Application error: {:?}", err);
    }

    Ok(())
}

/// Main dashboard application state
#[derive(Debug)]
struct SystemDashboard {
    // Application state
    should_quit: bool,
    current_view: DashboardView,
    selected_panel: usize,
    is_paused: bool,

    // Real-time data
    last_update: Instant,
    update_interval: Duration,

    // System metrics with historical data
    cpu_usage: MetricHistory,
    memory_usage: MetricHistory,
    network_upload: MetricHistory,
    network_download: MetricHistory,
    disk_usage: MetricHistory,

    // Process information
    processes: Vec<ProcessInfo>,

    // System information
    system_info: SystemInfo,

    // Network statistics
    network_stats: NetworkStats,

    // Performance counters
    performance_counters: PerformanceCounters,
}

/// Different dashboard views available
#[derive(Debug, Clone, Copy, PartialEq)]
enum DashboardView {
    Overview,
    Performance,
    Processes,
    Network,
    Storage,
}

/// Historical metric data with configurable retention
#[derive(Debug)]
struct MetricHistory {
    values: VecDeque<f64>,
    max_size: usize,
    current: f64,
    min: f64,
    max: f64,
    average: f64,
}

/// Process information structure
#[derive(Debug, Clone)]
struct ProcessInfo {
    name: String,
    pid: u32,
    cpu_percent: f64,
    memory_mb: f64,
    status: ProcessStatus,
}

/// Process status enumeration
#[derive(Debug, Clone)]
enum ProcessStatus {
    Running,
    Sleeping,
    Stopped,
    Zombie,
}

/// System information structure
#[derive(Debug)]
struct SystemInfo {
    os_name: String,
    kernel_version: String,
    architecture: String,
    hostname: String,
    uptime: Duration,
    boot_time: Instant,
    cpu_cores: u32,
    total_memory_gb: f64,
}

/// Network statistics
#[derive(Debug)]
struct NetworkStats {
    active_connections: u32,
    packets_sent: u64,
    packets_received: u64,
    bytes_sent: u64,
    bytes_received: u64,
    interface_count: u32,
}

/// Performance counters for detailed metrics
#[derive(Debug)]
struct PerformanceCounters {
    context_switches: u64,
    interrupts: u64,
    system_calls: u64,
    page_faults: u64,
    cache_hits: u64,
    cache_misses: u64,
}

impl SystemDashboard {
    /// Create a new system dashboard with initialized data
    fn new() -> Self {
        let now = Instant::now();

        Self {
            should_quit: false,
            current_view: DashboardView::Overview,
            selected_panel: 0,
            is_paused: false,
            last_update: now,
            update_interval: Duration::from_millis(500),

            cpu_usage: MetricHistory::new(100),
            memory_usage: MetricHistory::new(100),
            network_upload: MetricHistory::new(100),
            network_download: MetricHistory::new(100),
            disk_usage: MetricHistory::new(100),

            processes: Self::generate_sample_processes(),
            system_info: SystemInfo::new(),
            network_stats: NetworkStats::new(),
            performance_counters: PerformanceCounters::new(),
        }
    }

    /// Generate sample process data for demonstration
    fn generate_sample_processes() -> Vec<ProcessInfo> {
        vec![
            ProcessInfo {
                name: "chrome.exe".to_string(),
                pid: 1234,
                cpu_percent: 15.2,
                memory_mb: 512.8,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "node.exe".to_string(),
                pid: 5678,
                cpu_percent: 8.7,
                memory_mb: 256.4,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "rust-analyzer".to_string(),
                pid: 9012,
                cpu_percent: 5.1,
                memory_mb: 128.2,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "firefox.exe".to_string(),
                pid: 3456,
                cpu_percent: 4.3,
                memory_mb: 384.6,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "code.exe".to_string(),
                pid: 7890,
                cpu_percent: 3.8,
                memory_mb: 192.3,
                status: ProcessStatus::Running,
            },
        ]
    }

    /// Update system metrics with simulated real-time data
    fn update_metrics(&mut self) {
        if self.is_paused {
            return;
        }

        let now = Instant::now();
        if now.duration_since(self.last_update) >= self.update_interval {
            // Simulate CPU usage fluctuation
            let cpu_delta = (fastrand::f64() - 0.5) * 10.0;
            let new_cpu = (self.cpu_usage.current + cpu_delta).clamp(0.0, 100.0);
            self.cpu_usage.add_value(new_cpu);

            // Simulate memory usage with gradual changes
            let mem_delta = (fastrand::f64() - 0.5) * 2.0;
            let new_mem = (self.memory_usage.current + mem_delta).clamp(10.0, 90.0);
            self.memory_usage.add_value(new_mem);

            // Simulate network activity
            let upload_base = 1.2 + (fastrand::f64() - 0.5) * 0.8;
            let download_base = 5.8 + (fastrand::f64() - 0.5) * 3.0;
            self.network_upload.add_value(upload_base.max(0.0));
            self.network_download.add_value(download_base.max(0.0));

            // Simulate disk usage (slower changes)
            if fastrand::f64() < 0.1 {
                let disk_delta = (fastrand::f64() - 0.5) * 0.5;
                let new_disk = (self.disk_usage.current + disk_delta).clamp(20.0, 85.0);
                self.disk_usage.add_value(new_disk);
            }

            // Update process CPU usage
            for process in &mut self.processes {
                let delta = (fastrand::f64() - 0.5) * 2.0;
                process.cpu_percent = (process.cpu_percent + delta).clamp(0.0, 25.0);
            }

            // Update network statistics
            self.network_stats.packets_sent += fastrand::u64(10..100);
            self.network_stats.packets_received += fastrand::u64(50..500);
            self.network_stats.active_connections = 40 + fastrand::u32(0..20);

            // Update performance counters
            self.performance_counters.context_switches += fastrand::u64(100..1000);
            self.performance_counters.interrupts += fastrand::u64(50..500);
            self.performance_counters.system_calls += fastrand::u64(200..2000);

            self.last_update = now;
        }
    }

    /// Handle keyboard input for navigation and control
    fn handle_input(&mut self, key: KeyCode) {
        match key {
            KeyCode::Char('q') | KeyCode::Char('Q') => {
                self.should_quit = true;
            }
            KeyCode::Char(' ') => {
                self.is_paused = !self.is_paused;
            }
            KeyCode::Char('r') | KeyCode::Char('R') => {
                self.reset_metrics();
            }
            KeyCode::Char('1') => self.current_view = DashboardView::Overview,
            KeyCode::Char('2') => self.current_view = DashboardView::Performance,
            KeyCode::Char('3') => self.current_view = DashboardView::Processes,
            KeyCode::Char('4') => self.current_view = DashboardView::Network,
            KeyCode::Char('5') => self.current_view = DashboardView::Storage,
            KeyCode::Tab => {
                self.selected_panel = (self.selected_panel + 1) % 4;
            }
            KeyCode::BackTab => {
                self.selected_panel = if self.selected_panel == 0 {
                    3
                } else {
                    self.selected_panel - 1
                };
            }
            _ => {}
        }
    }

    /// Reset all metrics to initial values
    fn reset_metrics(&mut self) {
        self.cpu_usage.reset();
        self.memory_usage.reset();
        self.network_upload.reset();
        self.network_download.reset();
        self.disk_usage.reset();
        self.performance_counters = PerformanceCounters::new();
    }

    /// Render the main dashboard interface using RSX
    fn render(&self) -> VNode {
        match self.current_view {
            DashboardView::Overview => self.render_overview(),
            DashboardView::Performance => self.render_performance(),
            DashboardView::Processes => self.render_processes(),
            DashboardView::Network => self.render_network(),
            DashboardView::Storage => self.render_storage(),
        }
    }

    /// Render the overview dashboard with all main metrics
    fn render_overview(&self) -> VNode {
        let status_text = format!(
            "System Online | CPU: {:.1}% | Memory: {:.1}GB/{:.1}GB | Uptime: {}",
            self.cpu_usage.current,
            self.memory_usage.current * self.system_info.total_memory_gb / 100.0,
            self.system_info.total_memory_gb,
            self.format_uptime()
        );

        let _cpu_bar = self.create_progress_bar(self.cpu_usage.current, 50);
        let _memory_bar = self.create_progress_bar(self.memory_usage.current, 50);

        let processes_text = self
            .processes
            .iter()
            .take(5)
            .map(|p| format!("• {} - {:.1}% CPU", p.name, p.cpu_percent))
            .collect::<Vec<_>>()
            .join("\n");

        let network_text = format!(
            "↑ Upload: {:.1} MB/s\n↓ Download: {:.1} MB/s\n📡 Connections: {} active\n🌐 Packets: {} sent, {} received",
            self.network_upload.current,
            self.network_download.current,
            self.network_stats.active_connections,
            self.network_stats.packets_sent,
            self.network_stats.packets_received
        );

        let footer_text = format!(
            "OS: {} | Kernel: {} | Arch: {} | [1-5] Views | [Space] Pause | [R] Reset | [Q] Quit {}",
            self.system_info.os_name,
            self.system_info.kernel_version,
            self.system_info.architecture,
            if self.is_paused {
                "⏸ PAUSED"
            } else {
                "▶ LIVE"
            }
        );

        rsx! {
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Min(0),
                    Constraint::Length(3)
                ]}
            >
                <Block
                    title={"🖥️  Professional System Dashboard - Overview"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Cyan).add_modifier(Modifier::BOLD)}
                >
                    <Layout
                        direction={Direction::Vertical}
                        constraints={vec![
                            Constraint::Length(3),
                            Constraint::Min(0)
                        ]}
                    >
                        <Block
                            title={"📊 System Status"}
                            borders={Borders::ALL}
                            border_style={if self.selected_panel == 0 {
                                Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                            } else {
                                Style::default().fg(Color::Green)
                            }}
                        >
                            <Paragraph
                                style={Style::default().fg(Color::White)}
                                alignment={Alignment::Center}
                            >
                                {status_text}
                            </Paragraph>
                        </Block>

                        <Layout
                            direction={Direction::Horizontal}
                            constraints={vec![
                                Constraint::Percentage(50),
                                Constraint::Percentage(50)
                            ]}
                        >
                            <Layout
                                direction={Direction::Vertical}
                                constraints={vec![
                                    Constraint::Percentage(50),
                                    Constraint::Percentage(50)
                                ]}
                            >
                                <Block
                                    title={format!("⚡ CPU Usage - {:.1}% (Avg: {:.1}%)", self.cpu_usage.current, self.cpu_usage.average)}
                                    borders={Borders::ALL}
                                    border_style={if self.selected_panel == 1 {
                                        Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                                    } else {
                                        Style::default().fg(Color::Red)
                                    }}
                                >
                                    <Gauge
                                        ratio={self.cpu_usage.current / 100.0}
                                        label={"CPU Usage"}
                                        gauge_style={Style::default().fg(Color::Red)}
                                        use_unicode={true}
                                    />
                                </Block>

                                <Block
                                    title={format!("💾 Memory Usage - {:.1}% (Avg: {:.1}%)", self.memory_usage.current, self.memory_usage.average)}
                                    borders={Borders::ALL}
                                    border_style={if self.selected_panel == 2 {
                                        Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                                    } else {
                                        Style::default().fg(Color::Blue)
                                    }}
                                >
                                    <Gauge
                                        ratio={self.memory_usage.current / 100.0}
                                        label={"Memory Usage"}
                                        gauge_style={Style::default().fg(Color::Blue)}
                                        use_unicode={true}
                                    />
                                </Block>
                                </Block>
                            </Layout>

                            <Layout
                                direction={Direction::Vertical}
                                constraints={vec![
                                    Constraint::Percentage(50),
                                    Constraint::Percentage(50)
                                ]}
                            >
                                <Block
                                    title={"🔄 Top Processes"}
                                    borders={Borders::ALL}
                                    border_style={if self.selected_panel == 3 {
                                        Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                                    } else {
                                        Style::default().fg(Color::Magenta)
                                    }}
                                >
                                    <Paragraph style={Style::default().fg(Color::Magenta)}>
                                        {processes_text}
                                    </Paragraph>
                                </Block>

                                <Block
                                    title={"🌐 Network Activity"}
                                    borders={Borders::ALL}
                                    border_style={Style::default().fg(Color::Cyan)}
                                >
                                    <Paragraph style={Style::default().fg(Color::Cyan)}>
                                        {network_text}
                                    </Paragraph>
                                </Block>
                            </Layout>
                        </Layout>
                    </Layout>
                </Block>
                <Block
                    title={"ℹ️  System Information & Controls"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Gray)}
                >
                    <Paragraph
                        style={Style::default().fg(Color::White)}
                        alignment={Alignment::Center}
                    >
                        {footer_text}
                    </Paragraph>
                </Block>
            </Layout>
        }
    }

    /// Render the performance view with detailed metrics
    fn render_performance(&self) -> VNode {
        let perf_text = format!(
            "Context Switches: {}\nInterrupts: {}\nSystem Calls: {}\nPage Faults: {}",
            self.performance_counters.context_switches,
            self.performance_counters.interrupts,
            self.performance_counters.system_calls,
            self.performance_counters.page_faults
        );

        rsx! {
            <Layout
                direction={Direction::Vertical}
                constraints={vec![Constraint::Min(0)]}
            >
                <Block
                    title={"⚡ Performance Metrics"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)}
                >
                    <Paragraph style={Style::default().fg(Color::Yellow)}>
                        {perf_text}
                    </Paragraph>
                </Block>
            </Layout>
        }
    }

    /// Render the processes view with detailed process information
    fn render_processes(&self) -> VNode {
        let processes_text = self
            .processes
            .iter()
            .map(|p| {
                format!(
                    "PID: {} | {} | CPU: {:.1}% | Memory: {:.1}MB | Status: {:?}",
                    p.pid, p.name, p.cpu_percent, p.memory_mb, p.status
                )
            })
            .collect::<Vec<_>>()
            .join("\n");

        rsx! {
            <Layout
                direction={Direction::Vertical}
                constraints={vec![Constraint::Min(0)]}
            >
                <Block
                    title={"🔄 Process Manager"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Magenta).add_modifier(Modifier::BOLD)}
                >
                    <Paragraph style={Style::default().fg(Color::Magenta)}>
                        {processes_text}
                    </Paragraph>
                </Block>
            </Layout>
        }
    }

    /// Render the network view with detailed network statistics
    fn render_network(&self) -> VNode {
        let network_details = format!(
            "Active Connections: {}\nPackets Sent: {}\nPackets Received: {}\nBytes Sent: {}\nBytes Received: {}\nInterfaces: {}",
            self.network_stats.active_connections,
            self.network_stats.packets_sent,
            self.network_stats.packets_received,
            self.network_stats.bytes_sent,
            self.network_stats.bytes_received,
            self.network_stats.interface_count
        );

        rsx! {
            <Layout
                direction={Direction::Vertical}
                constraints={vec![Constraint::Min(0)]}
            >
                <Block
                    title={"🌐 Network Statistics"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Cyan).add_modifier(Modifier::BOLD)}
                >
                    <Paragraph style={Style::default().fg(Color::Cyan)}>
                        {network_details}
                    </Paragraph>
                </Block>
            </Layout>
        }
    }

    /// Render the storage view with disk usage information
    fn render_storage(&self) -> VNode {
        let disk_bar = self.create_progress_bar(self.disk_usage.current, 50);
        let storage_text = format!(
            "Disk Usage: {:.1}%\n{}\n\nTotal Space: 1TB\nFree Space: {:.1}GB\nUsed Space: {:.1}GB",
            self.disk_usage.current,
            disk_bar,
            (100.0 - self.disk_usage.current) * 10.0,
            self.disk_usage.current * 10.0
        );

        rsx! {
            <Layout
                direction={Direction::Vertical}
                constraints={vec![Constraint::Min(0)]}
            >
                <Block
                    title={"💽 Storage Information"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Green).add_modifier(Modifier::BOLD)}
                >
                    <Paragraph style={Style::default().fg(Color::Green)}>
                        {storage_text}
                    </Paragraph>
                </Block>
            </Layout>
        }
    }

    /// Create a visual progress bar for metrics
    fn create_progress_bar(&self, percentage: f64, width: usize) -> String {
        let filled = ((percentage / 100.0) * width as f64) as usize;
        let empty = width - filled;
        format!("{}{}", "█".repeat(filled), "░".repeat(empty))
    }

    /// Format uptime duration in a human-readable format
    fn format_uptime(&self) -> String {
        let uptime = self.system_info.uptime;
        let days = uptime.as_secs() / 86400;
        let hours = (uptime.as_secs() % 86400) / 3600;
        let minutes = (uptime.as_secs() % 3600) / 60;

        if days > 0 {
            format!("{}d {}h {}m", days, hours, minutes)
        } else if hours > 0 {
            format!("{}h {}m", hours, minutes)
        } else {
            format!("{}m", minutes)
        }
    }
}

impl MetricHistory {
    /// Create a new metric history with specified retention size
    fn new(max_size: usize) -> Self {
        Self {
            values: VecDeque::new(),
            max_size,
            current: 45.0, // Default starting value
            min: 45.0,
            max: 45.0,
            average: 45.0,
        }
    }

    /// Add a new value to the history and update statistics
    fn add_value(&mut self, value: f64) {
        self.values.push_back(value);
        if self.values.len() > self.max_size {
            self.values.pop_front();
        }

        self.current = value;
        self.min = self.values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        self.max = self.values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        self.average = self.values.iter().sum::<f64>() / self.values.len() as f64;
    }

    /// Reset the metric history to initial state
    fn reset(&mut self) {
        self.values.clear();
        self.current = 45.0;
        self.min = 45.0;
        self.max = 45.0;
        self.average = 45.0;
    }
}

impl SystemInfo {
    /// Create new system information with sample data
    fn new() -> Self {
        let now = Instant::now();
        Self {
            os_name: "Windows 11 Pro".to_string(),
            kernel_version: "10.0.22631".to_string(),
            architecture: "x64".to_string(),
            hostname: "WORKSTATION-01".to_string(),
            uptime: Duration::from_secs(432000), // 5 days
            boot_time: now,                      // Use current time as boot time for demo
            cpu_cores: 8,
            total_memory_gb: 16.0,
        }
    }
}

impl NetworkStats {
    /// Create new network statistics with initial values
    fn new() -> Self {
        Self {
            active_connections: 47,
            packets_sent: 1247,
            packets_received: 3891,
            bytes_sent: 1024 * 1024 * 150,     // 150 MB
            bytes_received: 1024 * 1024 * 890, // 890 MB
            interface_count: 3,
        }
    }
}

impl PerformanceCounters {
    /// Create new performance counters with initial values
    fn new() -> Self {
        Self {
            context_switches: 125000,
            interrupts: 89000,
            system_calls: 456000,
            page_faults: 12000,
            cache_hits: 890000,
            cache_misses: 45000,
        }
    }
}

/// Main application loop with event handling and rendering
fn run_dashboard<B: ratatui::backend::Backend>(
    terminal: &mut Terminal<B>,
    mut app: SystemDashboard,
) -> io::Result<()> {
    loop {
        // Update metrics with real-time simulation
        app.update_metrics();

        // Render the current view
        terminal.draw(|f| {
            let vnode = app.render();
            rink_core::render_vnode(&vnode, f.area(), f.buffer_mut());
        })?;

        // Handle input events with timeout for real-time updates
        if event::poll(Duration::from_millis(100))? {
            if let Event::Key(key) = event::read()? {
                if key.kind == KeyEventKind::Press {
                    app.handle_input(key.code);
                }
            }
        }

        // Check if application should quit
        if app.should_quit {
            break;
        }
    }
    Ok(())
}
