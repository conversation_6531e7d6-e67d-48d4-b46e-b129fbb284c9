[package]
name = "rink_examples"
version = "0.1.0"
edition = "2024"

[[bin]]
name = "simple_demo"
path = "src/simple_demo.rs"

[[bin]]
name = "dashboard_demo"
path = "src/dashboard_demo.rs"

[[bin]]
name = "comprehensive_demo"
path = "src/comprehensive_demo.rs"

[[bin]]
name = "gauge_demo"
path = "src/gauge_demo.rs"

[dependencies]
rink = { workspace = true }
rink_core = { workspace = true }
ratatui = { workspace = true }
tokio = { workspace = true, features = ["full"] }
crossterm = { workspace = true }
fastrand = "2.0"
