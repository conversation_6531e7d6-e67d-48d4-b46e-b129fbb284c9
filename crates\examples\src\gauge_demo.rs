use ratatui::{
    layout::{Constraint, Direction},
    style::{Color, Style},
    widgets::Borders,
};
use rink::prelude::*;

/// Professional Gauge widget demonstration
fn build_gauge_demo() -> impl Renderable {
    rsx! {
        <Block
            title="Professional Gauge Widget Demo"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Length(5),
                    Constraint::Length(5),
                    Constraint::Length(5),
                    Constraint::Length(5),
                    Constraint::Min(0)
                ]}
            >
                // CPU Usage Gauge
                <Block
                    title="CPU Usage"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Green)}
                >
                    <Gauge
                        percent={75}
                        label={"CPU: 75%"}
                        gauge_style={Style::default().fg(Color::Green)}
                    />
                </Block>

                // Memory Usage Gauge
                <Block
                    title="Memory Usage"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Yellow)}
                >
                    <Gauge
                        ratio={0.60}
                        label={"RAM: 4.8GB/8GB"}
                        gauge_style={Style::default().fg(Color::Yellow)}
                    />
                </Block>

                // Disk Usage Gauge
                <Block
                    title="Disk Usage"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Red)}
                >
                    <Gauge
                        percent={85}
                        label={"Disk: 850GB/1TB"}
                        gauge_style={Style::default().fg(Color::Red)}
                        use_unicode={true}
                    />
                </Block>

                // Network Activity Gauge
                <Block
                    title="Network Activity"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Blue)}
                >
                    <Gauge
                        ratio={0.35}
                        label={"Network: 35 Mbps"}
                        gauge_style={Style::default().fg(Color::Blue)}
                        use_unicode={true}
                    />
                </Block>

                // Instructions
                <Block
                    title="Instructions"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::White)}
                >
                    <Paragraph
                        style={Style::default().fg(Color::White)}
                        alignment={Alignment::Center}
                    >
                        {"Professional Gauge widgets with React-like RSX syntax!\nPress 'q' to quit"}
                    </Paragraph>
                </Block>
            </Layout>
        </Block>
    }
}

/// Entry point for the gauge demo
fn main() -> Result<(), Box<dyn std::error::Error>> {
    let demo = build_gauge_demo();
    render(demo)
}
