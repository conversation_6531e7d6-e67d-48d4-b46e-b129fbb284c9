use proc_macro2::TokenStream as TokenStream2;
use quote::{ToTokens, quote};
use std::collections::HashMap;

use crate::internals::ErrorContext;
use crate::parser::{RsxCodeGenerator, RsxValidator, validation_utils};
use crate::utils::Symbol;
use rstml::node::{Node, NodeAttribute};

/// Ratatui-specific RSX validator
pub struct RatatuiValidator {
    valid_props: HashMap<&'static str, Vec<&'static str>>,
    typo_suggestions: HashMap<&'static str, &'static str>,
    supported_widgets: Vec<Symbol>,
}

impl RatatuiValidator {
    pub fn new() -> Self {
        let mut valid_props = HashMap::new();

        // Block widget valid props
        valid_props.insert(
            "Block",
            vec![
                "title",
                "title_alignment",
                "title_style",
                "title_position",
                "borders",
                "border_style",
                "border_type",
                "style",
                "padding",
            ],
        );

        // Paragraph widget valid props
        valid_props.insert("Paragraph", vec!["style", "alignment", "wrap", "scroll"]);

        // Layout widget valid props
        valid_props.insert("Layout", vec!["direction", "constraints", "margin", "flex"]);

        // Line widget valid props (uses Paragraph internally)
        valid_props.insert("Line", vec!["style", "alignment"]);

        // Gauge widget valid props
        valid_props.insert(
            "Gauge",
            vec![
                "block",
                "percent",
                "ratio",
                "label",
                "style",
                "gauge_style",
                "use_unicode",
            ],
        );

        let mut typo_suggestions = HashMap::new();

        // Common typos for style-related props
        typo_suggestions.insert("styl", "style");
        typo_suggestions.insert("styel", "style");
        typo_suggestions.insert("stle", "style");

        // Common typos for alignment
        typo_suggestions.insert("alignement", "alignment");
        typo_suggestions.insert("aligment", "alignment");
        typo_suggestions.insert("align", "alignment");

        // Common typos for borders
        typo_suggestions.insert("boarder", "borders");
        typo_suggestions.insert("boarders", "borders");
        typo_suggestions.insert("border", "borders");

        // Common typos for title
        typo_suggestions.insert("titel", "title");
        typo_suggestions.insert("titl", "title");

        // Common typos for direction
        typo_suggestions.insert("direcction", "direction");
        typo_suggestions.insert("directon", "direction");

        // Common typos for constraints
        typo_suggestions.insert("constrains", "constraints");
        typo_suggestions.insert("constraint", "constraints");

        let supported_widgets = vec![
            Symbol::new("Layout"),
            Symbol::new("Block"),
            Symbol::new("Paragraph"),
            Symbol::new("Line"),
            Symbol::new("Gauge"),
        ];

        Self {
            valid_props,
            typo_suggestions,
            supported_widgets,
        }
    }
}

impl RsxValidator for RatatuiValidator {
    fn validate_node(&self, node: &Node, error_ctx: &ErrorContext) {
        match node {
            Node::Element(element) => {
                let name_str = element.name().to_string();

                // Empty element name validation
                if name_str.is_empty() {
                    error_ctx.add_error_with_span(element.name(), "Element name cannot be empty");
                    return;
                }

                // Widget type validation using Symbol constants
                if !self
                    .supported_widgets
                    .iter()
                    .any(|widget| widget == &name_str)
                {
                    // Check if it might be a component (starts with uppercase)
                    if name_str.chars().next().unwrap_or('a').is_uppercase() {
                        // This is likely a component, which is acceptable
                    } else {
                        error_ctx.add_error_with_span(element.name(), format!(
                            "Unsupported widget type '{}'. Supported widgets: Layout, Block, Paragraph, Line",
                            name_str
                        ));
                    }
                }

                // Validate attributes
                self.validate_element_attributes(element, error_ctx);

                // Recursively validate children
                for child in &element.children {
                    self.validate_node(child, error_ctx);
                }
            }
            Node::Text(_) | Node::Block(_) => {
                // Text and block nodes don't need validation
            }
            _ => {
                error_ctx.add_error_with_span(node, "Unsupported RSX node type");
            }
        }
    }
}

impl RatatuiValidator {
    /// Validate attributes for an element
    fn validate_element_attributes(
        &self,
        element: &rstml::node::NodeElement,
        error_ctx: &ErrorContext,
    ) {
        let widget_name = element.name().to_string();

        // Skip validation for unknown widget types (might be custom components)
        if let Some(valid_props_list) = self.valid_props.get(widget_name.as_str()) {
            for attr in element.attributes() {
                if let NodeAttribute::Attribute(attr) = attr {
                    let prop_name = attr.key.to_string();

                    // Check if prop name is valid
                    if !valid_props_list.contains(&prop_name.as_str()) {
                        // Check for typo suggestions
                        if let Some(suggestion) = self.typo_suggestions.get(prop_name.as_str()) {
                            error_ctx.add_error_with_span(
                                &attr.key,
                                format!(
                                    "Invalid prop '{}' for {} widget. Did you mean '{}'?",
                                    prop_name, widget_name, suggestion
                                ),
                            );
                        } else {
                            // Find closest match using string distance
                            let closest_match = validation_utils::find_closest_prop_match(
                                &prop_name,
                                valid_props_list,
                            );

                            if let Some(closest) = closest_match {
                                error_ctx.add_error_with_span(
                                    &attr.key,
                                    format!(
                                        "Invalid prop '{}' for {} widget. Did you mean '{}'? Valid props: {}",
                                        prop_name, widget_name, closest,
                                        valid_props_list.join(", ")
                                    ),
                                );
                            } else {
                                error_ctx.add_error_with_span(
                                    &attr.key,
                                    format!(
                                        "Invalid prop '{}' for {} widget. Valid props: {}",
                                        prop_name,
                                        widget_name,
                                        valid_props_list.join(", ")
                                    ),
                                );
                            }
                        }
                    }

                    // Additional validation for empty attribute names
                    if prop_name.is_empty() {
                        error_ctx.add_error_with_span(&attr.key, "Attribute name cannot be empty");
                    }
                }
            }
        }
    }
}

/// Ratatui-specific RSX code generator
pub struct RatatuiCodeGenerator;

impl RatatuiCodeGenerator {
    pub fn new() -> Self {
        Self
    }

    /// Collect element attributes as token streams for code generation
    fn collect_element_attributes(&self, element: &rstml::node::NodeElement) -> Vec<TokenStream2> {
        element
            .attributes()
            .iter()
            .filter_map(|attr| {
                if let NodeAttribute::Attribute(attr) = attr {
                    let name_ident =
                        syn::Ident::new(&attr.key.to_string(), proc_macro2::Span::call_site());
                    if let Some(value_node) = attr.value() {
                        if let Ok(expr) = syn::parse2::<syn::Expr>(value_node.to_token_stream()) {
                            Some(quote! { .#name_ident(#expr) })
                        } else {
                            None
                        }
                    } else {
                        // Boolean attribute
                        Some(quote! { .#name_ident(true) })
                    }
                } else {
                    None
                }
            })
            .collect()
    }

    /// Generate Layout-specific code
    fn generate_layout_code(
        &self,
        element: &rstml::node::NodeElement,
        error_ctx: &ErrorContext,
    ) -> TokenStream2 {
        let name_ident =
            syn::Ident::new(&element.name().to_string(), proc_macro2::Span::call_site());
        let attributes = self.collect_element_attributes(element);
        let children = element
            .children
            .iter()
            .map(|child| self.generate_node_code(child, error_ctx))
            .collect::<Vec<_>>();

        quote! {
            #[allow(unused_braces)]
            rink_core::VNode::Widget {
                widget: std::rc::Rc::new(
                    rink_core::LayoutWidget::new(
                        #name_ident::default()
                            #(#attributes)*,
                        vec![#(#children),*]
                    )
                ),
                render_fn: rink_core::create_layout_render_fn(),
                key: None,
            }
        }
    }

    /// Generate Block-specific code
    fn generate_block_code(
        &self,
        element: &rstml::node::NodeElement,
        error_ctx: &ErrorContext,
    ) -> TokenStream2 {
        let name_ident =
            syn::Ident::new(&element.name().to_string(), proc_macro2::Span::call_site());
        let attributes = self.collect_element_attributes(element);
        let children = element
            .children
            .iter()
            .map(|child| self.generate_node_code(child, error_ctx))
            .collect::<Vec<_>>();

        if children.is_empty() {
            quote! {
                #[allow(unused_braces)]
                rink_core::VNode::Widget {
                    widget: std::rc::Rc::new(
                        #name_ident::default()
                            #(#attributes)*
                    ),
                    render_fn: rink_core::create_block_render_fn(),
                    key: None,
                }
            }
        } else {
            quote! {
                #[allow(unused_braces)]
                rink_core::VNode::Widget {
                    widget: std::rc::Rc::new(
                        rink_core::BlockWithChildren::new(
                            #name_ident::default()
                                #(#attributes)*,
                            vec![#(#children),*]
                        )
                    ),
                    render_fn: rink_core::create_block_with_children_render_fn(),
                    key: None,
                }
            }
        }
    }

    /// Generate Paragraph-specific code
    fn generate_paragraph_code(
        &self,
        element: &rstml::node::NodeElement,
        error_ctx: &ErrorContext,
    ) -> TokenStream2 {
        let name_ident =
            syn::Ident::new(&element.name().to_string(), proc_macro2::Span::call_site());
        let attributes = self.collect_element_attributes(element);
        let text_content = self.collect_text_content(&element.children, error_ctx);

        quote! {
            #[allow(unused_braces)]
            rink_core::VNode::Widget {
                widget: std::rc::Rc::new(
                    #name_ident::new(#text_content)
                        #(#attributes)*
                ),
                render_fn: rink_core::create_paragraph_render_fn(),
                key: None,
            }
        }
    }

    /// Generate Line-specific code (uses Paragraph internally)
    fn generate_line_code(
        &self,
        element: &rstml::node::NodeElement,
        error_ctx: &ErrorContext,
    ) -> TokenStream2 {
        let attributes = self.collect_element_attributes(element);
        let text_content = self.collect_text_content(&element.children, error_ctx);

        quote! {
            #[allow(unused_braces)]
            rink_core::VNode::Widget {
                widget: std::rc::Rc::new(
                    Paragraph::new(#text_content)
                        #(#attributes)*
                ),
                render_fn: rink_core::create_paragraph_render_fn(),
                key: None,
            }
        }
    }

    /// Collect text content from Node children
    fn collect_text_content(&self, children: &[Node], _error_ctx: &ErrorContext) -> TokenStream2 {
        let text_parts: Vec<TokenStream2> = children
            .iter()
            .filter_map(|child| match child {
                Node::Text(text) => {
                    let text_content = text.value_string();
                    Some(quote! { #text_content })
                }
                Node::Block(block) => Some(quote! { #block }),
                Node::Element(_) => None, // Skip nested elements for text content
                _ => None,
            })
            .collect();

        if text_parts.is_empty() {
            quote! { "" }
        } else if text_parts.len() == 1 {
            text_parts.into_iter().next().unwrap()
        } else {
            quote! { format!("{}", vec![#(#text_parts.to_string()),*].join("")) }
        }
    }

    /// Generate Gauge-specific code
    fn generate_gauge_code(
        &self,
        element: &rstml::node::NodeElement,
        _error_ctx: &ErrorContext,
    ) -> TokenStream2 {
        let name_ident =
            syn::Ident::new(&element.name().to_string(), proc_macro2::Span::call_site());
        let attributes = self.collect_element_attributes(element);

        quote! {
            #[allow(unused_braces)]
            rink_core::VNode::Widget {
                widget: std::rc::Rc::new(
                    ratatui::widgets::#name_ident::default()
                        #(#attributes)*
                ),
                render_fn: rink_core::create_gauge_render_fn(),
                key: None,
            }
        }
    }
}

impl RsxCodeGenerator for RatatuiCodeGenerator {
    fn generate_node_code(&self, node: &Node, error_ctx: &ErrorContext) -> TokenStream2 {
        match node {
            Node::Element(element) => {
                let widget_name = element.name().to_string();

                match widget_name.as_str() {
                    "Layout" => self.generate_layout_code(element, error_ctx),
                    "Block" => self.generate_block_code(element, error_ctx),
                    "Paragraph" => self.generate_paragraph_code(element, error_ctx),
                    "Line" => self.generate_line_code(element, error_ctx),
                    "Gauge" => self.generate_gauge_code(element, error_ctx),
                    _ => {
                        // For unknown widgets, generate a simple widget node
                        let name_ident =
                            syn::Ident::new(&widget_name, proc_macro2::Span::call_site());
                        let attributes = self.collect_element_attributes(element);

                        quote! {
                            #[allow(unused_braces)]
                            rink_core::VNode::Widget {
                                widget: std::rc::Rc::new(
                                    #name_ident::default()
                                        #(#attributes)*
                                ),
                                render_fn: std::rc::Rc::new(|widget: &dyn std::any::Any, area: ratatui::layout::Rect, buf: &mut ratatui::buffer::Buffer| {
                                    // Generic widget rendering - implement as needed
                                }),
                                key: None,
                            }
                        }
                    }
                }
            }
            Node::Text(text) => {
                let text_content = text.value_string();
                quote! { rink_core::VNode::Text(#text_content.to_string()) }
            }
            Node::Block(block) => {
                // Convert block expressions to text
                quote! { rink_core::VNode::Text(format!("{}", #block)) }
            }
            _ => {
                error_ctx
                    .add_error_with_span(node, "Unsupported RSX node type for code generation");
                quote! { rink_core::VNode::Text("Error".to_string()) }
            }
        }
    }
}
