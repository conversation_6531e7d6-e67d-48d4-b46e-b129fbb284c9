use proc_macro::TokenStream;
use proc_macro2::TokenStream as TokenStream2;
use quote::quote;

use crate::parser::RsxProcessor;
use crate::ratatui_rsx::{RatatuiCodeGenerator, RatatuiValidator};

// Main implementation of the rsx! macro using the general parser
pub fn rsx_impl(input: TokenStream) -> TokenStream {
    let input2: TokenStream2 = input.into();

    // Create the Ratatui-specific processor
    let validator = RatatuiValidator::new();
    let generator = RatatuiCodeGenerator::new();
    let processor = RsxProcessor::new(validator, generator);

    // Process the RSX input
    match processor.process(input2) {
        Ok(output) => {
            // Wrap the output with necessary imports
            quote! { { use rink_core::VNode; #output } }.into()
        }
        Err(err) => err.to_compile_error().into(),
    }
}
