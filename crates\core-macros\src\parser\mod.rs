use proc_macro2::TokenStream as TokenStream2;
use quote::quote;
use syn::Result;

use crate::internals::ErrorContext;
use rstml::{node::Node, parse2};

/// General RSX parser that can be reused across different crates
/// This module provides the core parsing functionality without being tied to specific widgets
///
/// Represents a parsed RSX input with clean architecture
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct RsxInput {
    pub nodes: Vec<Node>,
}

impl RsxInput {
    /// Parse RSX tokens directly into rstml Nodes for maximum genericity
    pub fn parse(input: TokenStream2) -> Result<Self> {
        let nodes = parse2(input)?;
        Ok(RsxInput { nodes })
    }
}

/// General validation trait that can be implemented for different widget systems
pub trait RsxValidator {
    /// Validate an RSX node and add errors to the context
    fn validate_node(&self, node: &Node, error_ctx: &ErrorContext);
}

/// General code generation trait that can be implemented for different widget systems
pub trait RsxCodeGenerator {
    /// Generate code for an RSX node
    fn generate_node_code(&self, node: &Node, error_ctx: &ErrorContext) -> TokenStream2;

    /// Generate code for multiple nodes
    fn generate_nodes_code(&self, nodes: &[Node], error_ctx: &ErrorContext) -> Vec<TokenStream2> {
        nodes
            .iter()
            .map(|node| self.generate_node_code(node, error_ctx))
            .collect()
    }
}

/// General RSX processor that combines parsing, validation, and code generation
pub struct RsxProcessor<V, G>
where
    V: RsxValidator,
    G: RsxCodeGenerator,
{
    pub validator: V,
    pub generator: G,
}

impl<V, G> RsxProcessor<V, G>
where
    V: RsxValidator,
    G: RsxCodeGenerator,
{
    /// Create a new RSX processor with validator and generator
    pub fn new(validator: V, generator: G) -> Self {
        Self {
            validator,
            generator,
        }
    }

    /// Process RSX input: parse, validate, and generate code
    pub fn process(&self, input: TokenStream2) -> Result<TokenStream2> {
        let error_ctx = ErrorContext::new();

        // Parse the input
        let rsx_input = RsxInput::parse(input)?;

        // Validate all nodes
        for node in &rsx_input.nodes {
            self.validator.validate_node(node, &error_ctx);
        }

        // Check for validation errors before proceeding to code generation
        error_ctx.check_errors()?;

        // Generate code (only if validation passed)
        let error_ctx = ErrorContext::new(); // Fresh context for code generation
        let generated_nodes = self
            .generator
            .generate_nodes_code(&rsx_input.nodes, &error_ctx);

        // Check for code generation errors
        error_ctx.check_errors()?;

        // Return the generated code
        if generated_nodes.len() == 1 {
            Ok(generated_nodes.into_iter().next().unwrap())
        } else {
            Ok(quote! {
                vec![#(#generated_nodes),*]
            })
        }
    }
}

/// Utility functions for common validation tasks
pub mod validation_utils {
    /// Find closest prop match using Levenshtein distance
    pub fn find_closest_prop_match<'a>(
        prop_name: &str,
        valid_props: &'a [&str],
    ) -> Option<&'a str> {
        let mut best_match = None;
        let mut best_distance = usize::MAX;

        for valid_prop in valid_props {
            let distance = levenshtein_distance(prop_name, valid_prop);
            // Only suggest if distance is reasonable (less than half the prop name length)
            if distance < prop_name.len() / 2 + 1 && distance < best_distance {
                best_distance = distance;
                best_match = Some(*valid_prop);
            }
        }

        best_match
    }

    /// Simple Levenshtein distance implementation for typo detection
    pub fn levenshtein_distance(s1: &str, s2: &str) -> usize {
        let len1 = s1.len();
        let len2 = s2.len();
        let mut matrix = vec![vec![0; len2 + 1]; len1 + 1];

        // Initialize first row and column
        for (i, row) in matrix.iter_mut().enumerate().take(len1 + 1) {
            row[0] = i;
        }
        for j in 0..=len2 {
            matrix[0][j] = j;
        }

        // Fill the matrix
        for (i, c1) in s1.chars().enumerate() {
            for (j, c2) in s2.chars().enumerate() {
                let cost = if c1 == c2 { 0 } else { 1 };
                matrix[i + 1][j + 1] = std::cmp::min(
                    std::cmp::min(
                        matrix[i][j + 1] + 1, // deletion
                        matrix[i + 1][j] + 1, // insertion
                    ),
                    matrix[i][j] + cost, // substitution
                );
            }
        }

        matrix[len1][len2]
    }
}
